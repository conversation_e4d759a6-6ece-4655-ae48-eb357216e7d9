2025-07-01 11:05:41 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 11:05:41 - NodeExecutor - DEBUG - Stopping internal Kafka consumer...
2025-07-01 11:05:42 - NodeExecutor - INFO - Internal Kafka consumer stopped.
2025-07-01 11:05:42 - NodeExecutor - INFO - NodeExecutor internal consumer stopped.
2025-07-01 11:05:42 - AgentExecutor - INFO - Stopping AgentExecutor internal consumer components...
2025-07-01 11:05:42 - AgentExecutor - DEBUG - Cancelling background consumer task...
2025-07-01 11:05:42 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 11:05:42 - AgentExecutor - DEBUG - Stopping internal Kafka consumer...
2025-07-01 11:05:42 - AgentExecutor - INFO - Internal Kafka consumer stopped.
2025-07-01 11:05:42 - AgentExecutor - INFO - AgentExecutor internal consumer stopped.
2025-07-01 11:05:42 - KafkaWorkflowConsumer - INFO - Closing database connections...
2025-07-01 11:05:42 - RedisManager - INFO - Redis connection closed.
2025-07-01 11:05:42 - RedisManager - INFO - Redis connection closed.
2025-07-01 11:05:43 - PostgresManager - INFO - PostgreSQL connection pool closed.
2025-07-01 11:05:43 - PostgresManager - ERROR - Error ensuring tables exist: connection pool is closed
2025-07-01 11:05:43 - RedisEventListener - INFO - Redis event listener thread stopped
2025-07-01 11:05:43 - RedisEventListener - INFO - Redis event listener stopped
2025-07-01 11:05:43 - KafkaWorkflowConsumer - INFO - Database connections closed
2025-07-01 11:05:43 - KafkaWorkflowConsumer - INFO - Consumer and producer stopped gracefully.
Pratham-ka-MacBook-Air:orchestration-engine prathamagarwal$ 
Pratham-ka-MacBook-Air:orchestration-engine prathamagarwal$ 
Pratham-ka-MacBook-Air:orchestration-engine prathamagarwal$ 
Pratham-ka-MacBook-Air:orchestration-engine prathamagarwal$ clear
Pratham-ka-MacBook-Air:orchestration-engine prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Starting Orchestration Engine
2025-07-01 11:05:48 - Main - INFO - Starting Server
2025-07-01 11:05:48 - Main - INFO - Connection at: **************:9092
2025-07-01 11:05:48 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 11:05:48 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 11:05:48 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 11:05:48 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 11:05:48 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 11:05:50 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 11:05:50 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 11:05:52 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 11:05:53 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 11:05:53 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 11:05:56 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 11:05:56 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 11:05:56 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 11:05:58 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 11:05:58 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 11:06:00 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 11:06:00 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 11:06:00 - RedisEventListener - INFO - Redis event listener started
2025-07-01 11:06:00 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 11:06:00 - StateManager - DEBUG - Using provided database connections
2025-07-01 11:06:00 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 11:06:00 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 11:06:00 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 11:06:00 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 11:06:00 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 11:06:00 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 11:06:00 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 11:06:00 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 11:06:01 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 11:06:01 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 11:06:03 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 11:06:03 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 11:06:03 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 11:06:08 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 11:06:14 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 11:06:14 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 11:06:14 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 11:06:20 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 11:06:20 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 11:06:20 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 11:06:27 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 11:06:27 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 11:06:27 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1111
2025-07-01 11:06:27 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751348163, 'task_type': 'workflow', 'data': {'workflow_id': '881b5ff6-e06a-400a-9872-a32e9ea5860d', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'can you create a blog on social media marketing', 'transition_id': 'AgenticAI-1751283770733'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 11:06:27 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-07-01 11:06:27 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/881b5ff6-e06a-400a-9872-a32e9ea5860d
2025-07-01 11:06:27 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 11:06:27 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "881b5ff6-e06a-400a-9872-a32e9ea5860d",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/6f90563d-541f-4d69-8ecb-dd4e1e31dc28.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/79523345-e5f5-40d8-9340-c0af9634f12d.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-30T10:21:23.823061",
    "updated_at": "2025-07-01T05:35:59.817947",
    "available_nodes": [
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-1751283770733"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 11:06:27 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 881b5ff6-e06a-400a-9872-a32e9ea5860d - server_script_path is optional
[DEBUG] Skipping field 'query' for transition 'transition-AgenticAI-1751283770733' (intended for 'AgenticAI-1751283770733')
[DEBUG] Processing user-dependent field 'query' for transition 'transition-AgenticAI-1751283770733'
[DEBUG] Target transition for field 'query': 'AgenticAI-1751283770733'
[DEBUG] Target transition 'AgenticAI-1751283770733' doesn't exist, using as fallback for 'transition-AgenticAI-1751283770733'
2025-07-01 11:06:27 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 11:06:27 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 11:06:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 11:06:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 11:06:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 11:06:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 11:06:28 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 11:06:28 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 11:06:28 - StateManager - DEBUG - Using provided database connections
2025-07-01 11:06:28 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 11:06:28 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 11:06:28 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 11:06:29 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 11:06:29 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 11:06:29 - StateManager - INFO - Built dependency map for 1 transitions
2025-07-01 11:06:29 - MCPToolExecutor - DEBUG - Set correlation ID to: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189 in tool_executor
2025-07-01 11:06:29 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 11:06:29 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 11:06:29 - NodeExecutor - DEBUG - Set correlation ID to: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189 in node_executor
2025-07-01 11:06:29 - AgentExecutor - DEBUG - Set correlation ID to: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189 in agent_executor
2025-07-01 11:06:29 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 11:06:29 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 11:06:29 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 11:06:29 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:29 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 11:06:29 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-1751283770733
2025-07-01 11:06:29 - StateManager - DEBUG - State: pending={'transition-AgenticAI-1751283770733'}, waiting=set(), completed=set()
2025-07-01 11:06:29 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-1751283770733
2025-07-01 11:06:29 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-1751283770733'}
2025-07-01 11:06:30 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:4eef9c70-6af1-4520-a2d1-964ca8cf1189'
2025-07-01 11:06:30 - RedisManager - DEBUG - Set key 'workflow_state:4eef9c70-6af1-4520-a2d1-964ca8cf1189' with TTL of 600 seconds
2025-07-01 11:06:30 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 11:06:30 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 11:06:30 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 11:06:30 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-1751283770733'}
2025-07-01 11:06:30 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 11:06:30 - StateManager - INFO - Terminated: False
2025-07-01 11:06:30 - StateManager - INFO - Pending transitions (0): []
2025-07-01 11:06:30 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 11:06:30 - StateManager - INFO - Completed transitions (0): []
2025-07-01 11:06:30 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 11:06:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 11:06:30 - StateManager - INFO - Workflow status: inactive
2025-07-01 11:06:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 11:06:30 - StateManager - INFO - Workflow status: inactive
2025-07-01 11:06:30 - StateManager - INFO - Workflow paused: False
2025-07-01 11:06:30 - StateManager - INFO - ==============================
2025-07-01 11:06:30 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-1751283770733
2025-07-01 11:06:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189):
2025-07-01 11:06:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, response: {'result': 'Starting execution of transition: transition-AgenticAI-1751283770733', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-1751283770733', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 11:06:30 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-1751283770733' (type=initial, execution_type=agent)
2025-07-01 11:06:30 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 11:06:30 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-1751283770733
2025-07-01 11:06:30 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 11:06:30 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 11:06:30 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 11:06:30 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 11:06:30 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-07-01 11:06:30 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-1751283770733' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'agent_config': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'autogen_agent_type': 'Assistant', 'input_variables': {'requirement': 'conclusion'}}}
2025-07-01 11:06:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189):
2025-07-01 11:06:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 11:06:30 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: e216c661-1a35-4928-97d4-3f83418a547a) with correlation_id: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 11:06:30 - AgentExecutor - INFO - Building component agent request for execution_type: response
agent config in build component agent request:  {'id': '05d38bdf-6882-412b-93e1-ba0a43c15a9a', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}
template variables in build component agent request:  {'requirement': 'conclusion'}
2025-07-01 11:06:30 - AgentExecutor - DEBUG - Added correlation_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189 to payload
2025-07-01 11:06:30 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'e216c661-1a35-4928-97d4-3f83418a547a', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '4eef9c70-6af1-4520-a2d1-964ca8cf1189', 'agent_type': 'component', 'execution_type': 'response', 'query': 'can you create a blog on social media marketing', 'variables': {'requirement': 'conclusion'}, 'agent_config': {'id': '05d38bdf-6882-412b-93e1-ba0a43c15a9a', 'name': 'AI Agent', 'description': 'blog writer', 'system_message': 'you are responsible for writing ${{requirement}} for the blog basd on the topic', 'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 11:06:30 - AgentExecutor - DEBUG - Request e216c661-1a35-4928-97d4-3f83418a547a sent successfully using provided producer.
2025-07-01 11:06:30 - AgentExecutor - DEBUG - Waiting for single response result for request e216c661-1a35-4928-97d4-3f83418a547a...
2025-07-01 11:06:30 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1111, corr_id: 4eef9c70-6af1-4520-a2d1-964ca8cf1189
2025-07-01 11:06:37 - AgentExecutor - DEBUG - Result consumer received message: Offset=24465
2025-07-01 11:06:37 - AgentExecutor - WARNING - Received error response for request_id e216c661-1a35-4928-97d4-3f83418a547a: Encountered error during Agent exception
2025-07-01 11:06:37 - AgentExecutor - ERROR - Error during agent execution e216c661-1a35-4928-97d4-3f83418a547a: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-1751283770733': Agent execution failed: Encountered error during Agent exceptionTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

2025-07-01 11:06:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 4eef9c70-6af1-4520-a2d1-964ca8cf1189):
2025-07-01 11:06:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, response: {'transition_id': 'transition-AgenticAI-1751283770733', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'status': 'failed', 'sequence': 2, 'workflow_status': 'running'}
2025-07-01 11:06:37 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception')]
2025-07-01 11:06:37 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-1751283770733: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-1751283770733: NoneType: None

2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 702, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 702, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

2025-07-01 11:06:37 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 478, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 379, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 361, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 702, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 330, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 341, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - KafkaWorkflowConsumer - INFO - Workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d' final status: failed, result: Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception
2025-07-01 11:06:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4eef9c70-6af1-4520-a2d1-964ca8cf1189, response: {'status': 'failed', 'result': "Exception in workflow '881b5ff6-e06a-400a-9872-a32e9ea5860d': Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-1751283770733: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Encountered error during Agent exception', 'error_type': 'Exception'}
2025-07-01 11:06:37 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 4eef9c70-6af1-4520-a2d1-964ca8cf1189 
2025-07-01 11:06:43 - AgentExecutor - DEBUG - Result consumer received message: Offset=24466
2025-07-01 11:06:43 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: e216c661-1a35-4928-97d4-3f83418a547a
2025-07-01 11:06:57 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 11:06:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 11:06:57 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 11:06:57 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
