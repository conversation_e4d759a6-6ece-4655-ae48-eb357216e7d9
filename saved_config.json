{"name": "Untitled Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 80, "y": 40}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"AgenticAI-1751283770733_query": {"node_id": "AgenticAI-1751283770733", "node_name": "marketing research ", "input_name": "query", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "AgenticAI-1751283770733", "type": "WorkflowNode", "position": {"x": 380, "y": -180}, "data": {"label": "marketing research ", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "type": "component", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "alibaba", "options": ["alibaba", "anthropic", "cline", "coding", "deepinfra", "deepseek", "google", "groq", "minimaxi", "mistral", "nebius", "netmind", "novita", "openai", "parasail", "perplexity", "together", "vertex", "xai", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": [], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "_dynamicFiltering": true, "_filterByField": "model_provider", "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": [], "_enhanced": true, "_enhancementTimestamp": 1751280891083, "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}, "_availableCredentials": []}, "config": {"id": "AgenticAI-1751283770733", "name": "AI Agent Executor", "model_provider": "openai", "base_url": "", "model_name": "gpt-4o-mini", "temperature": 0.7, "description": "blog writer", "execution_type": "response", "query": "", "system_message": "can you give me a topic for generating script based on the domain given by user \n\nthe output should only be the topic and nothing else\ndo not give any other additions \nif topic is \"marketing\" output just \"marketing\"", "termination_condition": "", "max_tokens": 1000, "input_variables": {"name": "pratham"}, "autogen_agent_type": "Assistant", "tools": []}}, "width": 208, "height": 232, "selected": true, "dragging": false, "style": {"opacity": 1}}, {"id": "MCP_DuckDuckGo_search-1751352755707", "type": "WorkflowNode", "position": {"x": 740, "y": -160}, "data": {"label": "DuckDuckGo - search", "type": "mcp", "originalType": "MCP_DuckDuckGo_search", "definition": {"name": "MCP_DuckDuckGo_search", "display_name": "DuckDuckGo - search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ", "category": "MCP Marketplace", "icon": "Cloud", "beta": true, "inputs": [{"name": "query", "display_name": "Query", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "max_results", "display_name": "Max Results", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "test", "display_name": "test", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_duckduckgo_search", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "035a8924-5153-4133-940e-ac0be0dbd32a", "server_path": "", "tool_name": "search", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"default": 10, "title": "Max Results", "type": "integer"}}, "required": ["query"], "title": "searchArguments", "type": "object"}, "output_schema": {"properties": {"test": {"type": "string", "description": "test", "title": "test"}}}}}, "config": {}}, "style": {"opacity": 1}, "width": 208, "height": 148, "selected": false, "positionAbsolute": {"x": 740, "y": -160}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "AgenticAI-1751283770733", "targetHandle": "query", "type": "default", "id": "reactflow__edge-start-nodeflow-AgenticAI-1751283770733query"}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "AgenticAI-1751283770733", "sourceHandle": "final_answer", "target": "MCP_DuckDuckGo_search-1751352755707", "targetHandle": "query", "type": "default", "id": "reactflow__edge-AgenticAI-1751283770733final_answer-MCP_DuckDuckGo_search-1751352755707query"}], "unconnected_nodes": [{"id": "AgenticAI-1751283766433", "type": "WorkflowNode", "position": {"x": 400, "y": 300}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "type": "component", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "alibaba", "options": ["alibaba", "anthropic", "cline", "coding", "deepinfra", "deepseek", "google", "groq", "minimaxi", "mistral", "nebius", "netmind", "novita", "openai", "parasail", "perplexity", "together", "vertex", "xai", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": [], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "_dynamicFiltering": true, "_filterByField": "model_provider", "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": [], "_enhanced": true, "_enhancementTimestamp": 1751280891083, "_providerIdMapping": {"alibaba": {"providerId": "137fa03d-a590-45cb-98b3-156c3a734740", "providerName": "alibaba", "isActive": true}, "anthropic": {"providerId": "aea5be12-9aea-4e74-85a9-72fb29600079", "providerName": "anthropic", "isActive": true}, "cline": {"providerId": "661aaecf-c7a1-4166-bb5a-bb8300ada45b", "providerName": "cline", "isActive": true}, "coding": {"providerId": "152ad359-213b-41a5-951a-3057de036307", "providerName": "coding", "isActive": true}, "deepinfra": {"providerId": "5fa7e842-3644-464d-af18-d188c81d7b45", "providerName": "deepinfra", "isActive": true}, "deepseek": {"providerId": "59605e2b-5a8a-4443-b9a8-3b86015712ef", "providerName": "deepseek", "isActive": true}, "google": {"providerId": "34178783-8099-4da9-b17d-9b65611136d6", "providerName": "google", "isActive": true}, "groq": {"providerId": "babd6416-991d-4d44-a70d-a6be0654b1a7", "providerName": "groq", "isActive": true}, "minimaxi": {"providerId": "a59f4d27-c58f-463b-a16c-899a22091cb1", "providerName": "minimaxi", "isActive": true}, "mistral": {"providerId": "996804db-2ac8-4c62-a9fe-53ee943f8b7a", "providerName": "mistral", "isActive": true}, "nebius": {"providerId": "17d2cd04-c0dc-4b08-a048-a7f473243ebb", "providerName": "nebius", "isActive": true}, "netmind": {"providerId": "efad9b08-b481-4040-b57f-873063a36beb", "providerName": "netmind", "isActive": true}, "novita": {"providerId": "acb072e1-f979-4472-840c-8b499734369d", "providerName": "novita", "isActive": true}, "openai": {"providerId": "be157187-39c0-4930-9ca9-3d127d464a28", "providerName": "openai", "isActive": true}, "parasail": {"providerId": "9dbc0526-2c57-4df8-91c4-3ef94972bd7c", "providerName": "parasail", "isActive": true}, "perplexity": {"providerId": "9d918de2-ec13-4fa8-bf88-df28bf37ac08", "providerName": "perplexity", "isActive": true}, "together": {"providerId": "4e61f654-af7a-4e31-80c8-64186772c0d3", "providerName": "together", "isActive": true}, "vertex": {"providerId": "f8cbad74-fdad-4990-a270-9ef90fcf27ea", "providerName": "vertex", "isActive": true}, "xai": {"providerId": "f0dc3323-1eff-4acd-9214-5fbb1440f5dc", "providerName": "xai", "isActive": true}, "Custom": {"providerId": "custom", "providerName": "Custom", "isActive": true}}, "_availableCredentials": []}, "config": {"id": "AgenticAI-1751283766433", "name": "AI Agent Executor", "model_provider": "alibaba", "base_url": "", "model_name": "", "temperature": 0.7, "description": "response giving ", "execution_type": "response", "query": "", "system_message": "you are responsible for writing ${{requirement}} for the blog basd on the topic", "termination_condition": "", "max_tokens": 1000, "input_variables": {"requirement": "introduction"}, "autogen_agent_type": "Assistant"}}, "width": 208, "height": 232, "selected": false, "dragging": false, "style": {"opacity": 0.5}}, {"id": "CombineTextComponent-1751283784983", "type": "WorkflowNode", "position": {"x": 660, "y": 200}, "data": {"label": "Combine Text", "type": "component", "originalType": "CombineTextComponent", "definition": {"name": "CombineTextComponent", "display_name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "category": "Processing", "icon": "Link", "type": "component", "beta": false, "requires_approval": false, "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.processing.combinetextcomponent", "interface_issues": []}, "config": {"main_input": "", "num_additional_inputs": "1", "separator": "", "input_1": "", "input_2": "", "input_3": "", "input_4": "", "input_5": "", "input_6": "", "input_7": "", "input_8": "", "input_9": "", "input_10": ""}}, "width": 208, "height": 148, "selected": false, "dragging": false, "style": {"opacity": 0.5}}]}, "start_node_data": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751283770733"}]}