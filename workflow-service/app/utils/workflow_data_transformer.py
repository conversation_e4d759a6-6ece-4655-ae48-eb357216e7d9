"""
Utility module for transforming workflow data from sample_workflow.json format
to the expected output format for workflow processing.
"""

from typing import Dict, Any, List
import json


def _should_skip_node(node: Dict[str, Any]) -> bool:
    """
    Determine if a node should be skipped during transformation.

    Args:
        node: The node to check

    Returns:
        True if the node should be skipped, False otherwise
    """
    node_data = node.get("data", {})
    original_type = node_data.get("originalType", "")

    # Skip StartNode and other system nodes
    skip_types = ["StartNode", "EndNode"]

    # Skip if it's a system node
    if original_type in skip_types:
        return True

    return False


def _is_tool_node(node: Dict[str, Any], edges: List[Dict[str, Any]]) -> bool:
    """
    Check if a node is connected as a tool to an AgenticAI node.

    Args:
        node: The node to check
        edges: List of edges in the workflow

    Returns:
        True if the node is connected to an AgenticAI node's tools handle
    """
    node_id = node.get("id")

    # Check if this node connects to any AgenticAI node's tools handle
    for edge in edges:
        if edge.get("source") == node_id and edge.get("targetHandle") == "tools":
            return True
    return False


def extract_mcp_and_component_nodes_exact_format(
    workflow_data: Dict[str, Any],
) -> List[Dict[str, Any]]:
    """
    Extract all nodes that are of type 'mcp', 'component', or 'agent' from workflow_data
    following the exact format specification.

    Navigation: nodes -> data -> check type in data

    Args:
        workflow_data: The workflow data in the format from sample_workflow.json

    Returns:
        List[Dict[str, Any]] containing all MCP, component, and agent nodes in exact format

    For component type:
        {
            "name": data.definition.name,
            "display_name": data.definition.display_name,
            "transition_id": "",
            "type": data.type
        }

    For mcp type:
        {
            "name": data.definition.name,
            "id": data.mcp_info.server_id,
            "type": data.type,
            "transition_id": "", transition-{mcp_id}
            "display_name": data.definition.display_name,
            "data": {
                "input_schema": data.mcp_info.input_schema,
                "output_schema": data.mcp_info.output_schema,
            }
        }

    For agent type:
        {
            "name": data.definition.name,
            "display_name": data.definition.display_name,
            "type": data.type,
            "transition_id": ""
        }
    """
    print("Extracting available nodes...")
    if not workflow_data or "nodes" not in workflow_data:
        return []

    nodes = workflow_data.get("nodes", [])
    edges = workflow_data.get("edges", [])
    result_nodes = []

    for node in nodes:
        # Navigate to nodes -> data
        node_data = node.get("data", {})

        transition_id = f"transition-{node.get('id')}"

        print(f"[TRANSITION_ID] {transition_id}")
        # Check type in data
        node_type = node_data.get("type", "")

        # Skip StartNode and other system nodes
        if _should_skip_node(node):
            continue

        # Skip tool nodes (nodes connected to AgenticAI tools handle)
        if _is_tool_node(node, edges):
            node_id = node.get("id")
            print(f"   ⏭️  SKIPPED: Tool node {node_id} (connected to agent tools handle)")
            continue

        # Only process MCP, component, and agent nodes
        if node_type not in ["mcp", "component", "agent"]:
            continue

        # Extract definition
        definition = node_data.get("definition", {})

        if node_type == "component":
            # Component format: {name, display_name, type, label}
            transformed_node = {
                "name": definition.get("name", ""),
                "display_name": definition.get("display_name", ""),
                "type": node_type,
                "transition_id": transition_id,
                "label": node_data.get("label", ""),
            }

        elif node_type == "mcp":
            # MCP format: {name, id, type, data: {display_name, input_schema, output_schema}, label}
            mcp_info = definition.get("mcp_info", {})

            transformed_node = {
                "name": definition.get("name", ""),
                "id": mcp_info.get("server_id", ""),
                "transition_id": transition_id,
                "type": node_type,
                "display_name": definition.get("display_name", ""),
                "label": node_data.get("label", ""),
                "data": {
                    "input_schema": mcp_info.get("input_schema", {}),
                    "output_schema": mcp_info.get("output_schema", {}),
                },
            }

        elif node_type == "agent":
            # Agent format: {name, display_name, type, transition_id, label}
            transformed_node = {
                "name": definition.get("name", ""),
                "display_name": definition.get("display_name", ""),
                "type": node_type,
                "transition_id": transition_id,
                "label": node_data.get("label", ""),
            }

        else:
            continue

        result_nodes.append(transformed_node)
    print("Available nodes:", result_nodes)
    return result_nodes


# if __name__ == "__main__":
#     schema_path = "sample_workflow.json"
#     with open(schema_path, "r") as f:
#         sample_workflow = json.load(f)

#     nodes = extract_mcp_and_component_nodes_exact_format(sample_workflow)
#     print(nodes)
