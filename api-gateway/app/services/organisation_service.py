import grpc
from typing import List, Optional
from app.core.config import settings
from app.grpc_ import organisation_pb2, organisation_pb2_grpc
import requests
from typing import List, Optional
from app.services.user_service import UserServiceClient


class OrganisationServiceClient:

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.ORGANISATION_SERVICE_HOST}:{settings.ORGANISATION_SERVICE_PORT}"
        )

        self.stub = organisation_pb2_grpc.OrganisationServiceStub(self.channel)
    
    # Organisation related services
    async def create_organisation(self, name: str, user_email: str, user_name: str, website_url: Optional[str] = None, logo: Optional[str] = None, industry: Optional[str] = None, created_by: str = None):
        """
        Create a new organisation in the system.
        
        Args:
            name: The name of the organisation (required)
            website_url: The organisation's website URL (optional)
            industry: The industry sector of the organisation (optional)
            created_by: User ID of the creator (optional, defaults to authenticated user)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """

        # Create the request object with the updated field names
        request = organisation_pb2.CreateOrganisationRequest(
            name=name,
            website_url=website_url if website_url else "",
            industry=industry if industry else "",
            logo=logo if logo else "",
            created_by=created_by if created_by else "",
            admin_name=user_name,
            admin_email=user_email,
        )
        
        try:
            response = self.stub.createOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_organisation(self, organisation_id: str, requester_user_id: str):
        request = organisation_pb2.GetOrganisationRequest(
            id=organisation_id
        )
        try:
            response = self.stub.getOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def invite_user(
        self,
        email: str,
        organisation_id: str,
        created_by: str,
        role: Optional[str] = None,
        department: Optional[str] = None,
        permission: Optional[str] = None
    ):
        """
        Create a new invitation for a user to join an organisation.
        
        Args:
            email: Email address of the user to invite
            organisation_id: ID of the organisation
            created_by: User ID of the person creating the invite
            role: Role to assign to the user (optional)
            department: Department to assign the user to (optional)
            permissions: List of specific permissions for the user (optional)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.InviteUserRequest(
            email=email,
            organisation_id=organisation_id,
            role=role if role else "",
            department=department if department else "",
            permission=permission if permission else "",
            created_by=created_by
        )

        try:
            response = self.stub.inviteUser(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    async def accept_invite_by_link(self, invite_token: str, auth_user_id: str, user_name: str, auth_user_email: str, accept: bool):
        """
        Accept an invitation using an invite link.
        
        Args:
            invite_link: The encoded invite link
            auth_user_id: User ID from authentication token
            auth_user_email: User email from authentication token
            
        Returns:
            The gRPC response containing the invite details
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object with just the invite link
        # The email will be extracted from auth token and passed to the service
        request = organisation_pb2.AcceptInviteByLinkRequest(
            invite_token=invite_token,
            current_user_email=auth_user_email,
            user_id=auth_user_id,
            user_name=user_name,
            accept=accept
        )

        try:
            response = self.stub.acceptInviteByLink(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
        
    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            from fastapi import HTTPException

            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            from fastapi import HTTPException

            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            from fastapi import HTTPException

            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            from fastapi import HTTPException

            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            from fastapi import HTTPException

            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            from fastapi import HTTPException

            raise HTTPException(status_code=400, detail=details)
        elif status_code == grpc.StatusCode.UNIMPLEMENTED:
            from fastapi import HTTPException

            raise HTTPException(status_code=501, detail=f"Feature not implemented: {details}")
        elif status_code == grpc.StatusCode.UNIMPLEMENTED:
            from fastapi import HTTPException

            raise HTTPException(status_code=501, detail=f"Feature not implemented: {details}")
        else:
            from fastapi import HTTPException

            raise HTTPException(status_code=500, detail="Internal server error")
            
    async def get_user_organisations(self, user_email: str):
        """
        Fetch all organisations that the user belongs to.
        
        Args:
            user_id: ID of the user to get organisations for
            
        Returns:
            UserOrganisationsResponse containing all organisations the user belongs to,
            with information about primary organisation and admin status
            
        Raises:
            GrpcError: If the gRPC call fails
        """

        request = organisation_pb2.GetUserOrganisationsRequest(
            user_id=user_email
        )
        
        try:
            response = self.stub.getUserOrganisations(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def create_department(
        self,
        organisation_id: str,
        name: str,
        description: Optional[str] = None,
        parent_department_id: Optional[str] = None,
        created_by: str = None,
        visibility: Optional[str] = None
    ):
        """
        Create a new department within an organisation.
        
        Args:
            organisation_id: ID of the organisation the department belongs to
            name: Name of the department
            description: Optional description of the department
            parent_department_id: Optional ID of the parent department (for hierarchical departments)
            created_by: User ID of the creator
            visibility: Optional visibility setting ("PUBLIC" or "PRIVATE")
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.CreateDepartmentRequest(
            organisation_id=organisation_id,
            name=name,
            description=description if description else "",
            parent_department_id=parent_department_id if parent_department_id else "",
            created_by=created_by if created_by else "",
            visibility=visibility if visibility else ""
        )
        
        try:
            response = self.stub.createDepartment(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def list_departments(
        self,
        organisation_id: str,
        user_id: str,
        page: int = 1,
        page_size: int = 10,
        search_term: Optional[str] = None,
        department_id: Optional[str] = None
    ):
        """
        List departments in an organisation with optional filtering and pagination.
        
        Args:
            organisation_id: ID of the organisation to list departments for
            page: Page number for pagination (starts at 1)
            page_size: Number of items per page
            search_term: Optional search term to filter departments by name or description
            department_id: Optional specific department ID to filter by
            
        Returns:
            The gRPC response containing the list of departments
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Create the request object
        request = organisation_pb2.ListDepartmentsRequest(
            organisation_id=organisation_id,
            page=page,
            page_size=page_size,
            user_id=user_id,
            search_term=search_term if search_term else "",
            department_id=department_id if department_id else ""
        )
        
        try:
            response = self.stub.listDepartments(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def getDepartmentUsers(self, org_id, dept_id=None, page=1, page_size=10):
        """
        Get users belonging to a specific department or all users in an organisation, with pagination.
        
        This method can be called in two ways:
        1. As a gRPC service method: getDepartmentUsers(request, context)
        2. As an internal method: getDepartmentUsers(org_id, dept_id=None, page=1, page_size=10)
        
        Args:
            org_id: Either a GetDepartmentUsersRequest (gRPC) or a string org_id (internal)
            dept_id_or_context: Either a grpc.ServicerContext (gRPC) or a string dept_id (internal, can be None)
            page: Page number (1-based, default: 1) - only used for internal method
            page_size: Number of items per page (default: 10) - only used for internal method
            
        Returns:
            When called as gRPC method: GetDepartmentUsersResponse
            When called internally: Tuple of (users_list, total_count, page, page_size)
        """ 
        request = organisation_pb2.GetDepartmentUsersRequest(
                organisation_id=org_id,
                department_id=dept_id or "",  # Convert None to empty string for protobuf
                page=page,
                page_size=page_size
            )      
        try:
            # Make the gRPC call
            response = self.stub.getDepartmentUsers(request)
            return response.users
            
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_inviter_invites(self, user_id: str, organisation_id: str, invite_type: str):
            """
            Fetch all invites created by a specific user (inviter).
            Can return either accepted invites (from Neo4j) or pending invites (from PostgreSQL).
            
            Args:
                user_id: ID of the user who created the invites
                invite_type: Type of invites to fetch ("ACCEPTED" or "PENDING")
                
            Returns:
                The gRPC response containing the list of invites
                
            Raises:
                GrpcError: If the gRPC call fails
            """
            # Create the request object
            request = organisation_pb2.ListInviterInvitesRequest(
                user_id=user_id,
                organisation_id=organisation_id,
                type=invite_type
            )
            
            try:
                response = self.stub.getInviterInvites(request)
                return response
            except grpc.RpcError as e:
                raise self._handle_error(e)
    async def add_source(
        self,
        organisation_id: str,
        source_type: int,  # SourceType enum value (0=GOOGLE_DRIVE, 1=SLACK)
        name: str,
        key: str,
        file_ids: Optional[List[str]] = None,
        jira_url: Optional[str] = None,
        jira_email: Optional[str] = None,
    ):
        """
        Add a new source with service account credentials to an organisation.
        
        Args:
            organisation_id: ID of the organisation
            source_type: Type of source (0=GOOGLE_DRIVE, 1=SLACK)
            name: Name/identifier for the source
            service_account_key: JSON string containing Service Account key (required)
            file_ids: Optional list of specific file IDs to sync
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.AddSourceRequest(
            organisation_id=organisation_id,
            type=source_type,
            name=name,
            key=key,
            file_ids=file_ids or [],
            jira_email=jira_email,
            jira_url=jira_url,
        )
        
        try:
            response = self.stub.addSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_sources(self, organisation_id: str):
        """
        List all sources for an organisation.
        
        Args:
            organisation_id: ID of the organisation to list sources for
            
        Returns:
            The gRPC response containing the list of sources
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.ListSourcesRequest(
            organisation_id=organisation_id
        )
        
        try:
            response = self.stub.listSources(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def grant_department_access(
        self,
        department_id: str,
        file_ids: List[str],
        folder_ids: List[str],
        user_id: str
    ):
        """
        Grant a department access to files and folders.
        
        Args:
            department_id: ID of the department to grant access to
            file_ids: List of file IDs to grant access to
            folder_ids: List of folder IDs to grant access to
            user_id: ID of the user granting the access
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.GrantDepartmentAccessRequest(
            department_id=department_id,
            file_ids=file_ids,
            folder_ids=folder_ids,
            user_id=user_id
        )
        
        try:
            response = self.stub.grantDepartmentAccess(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def batch_grant_department_access(
        self,
        department_data: List[dict],
        user_id: str
    ):
        """
        Grant multiple departments access to files and folders in a batch operation.
        
        Args:
            department_data: List of department access data dictionaries
            user_id: ID of the user granting the access
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        # Convert department data to protobuf format
        dept_access_list = []
        for dept_data in department_data:
            dept_access = organisation_pb2.DepartmentAccessData(
                department_id=dept_data.get("department_id", ""),
                file_ids=dept_data.get("file_ids", []),
                folder_ids=dept_data.get("folder_ids", [])
            )
            dept_access_list.append(dept_access)
        
        request = organisation_pb2.BatchGrantDepartmentAccessRequest(
            department_data=dept_access_list,
            user_id=user_id
        )
        
        try:
            response = self.stub.batchGrantDepartmentAccess(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_source(self, source_id: str, user_id: str):
        """
        Delete a source from an organisation.
        
        Args:
            source_id: ID of the source to delete
            user_id: ID of the user performing the deletion (must be admin)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.DeleteSourceRequest(
            source_id=source_id,
            user_id=user_id
        )
        
        try:
            response = self.stub.deleteSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def update_source_credentials(
        self,
        source_id: str,
        user_id: str,
        service_account_key: str
    ):
        """
        Update service account credentials for an existing source.
        
        Args:
            source_id: ID of the source to update
            user_id: ID of the user performing the update (must be admin)
            service_account_key: Service Account JSON key (required)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.UpdateSourceCredentialsRequest(
            source_id=source_id,
            user_id=user_id,
            service_account_key=service_account_key
        )
        
        try:
            response = self.stub.updateSourceCredentials(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def list_department_folders(
        self,
        organisation_id: str,
        department_ids: List[str]
    ):
        """
        List folders accessible by specified departments.
        
        Args:
            organisation_id: ID of the organisation
            department_ids: List of department IDs to get folders for
            
        Returns:
            The gRPC response containing the list of department folders
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.ListDepartmentFoldersRequest(
            organisation_id=organisation_id,
            department_ids=department_ids
        )
        
        try:
            response = self.stub.listDepartmentFolders(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def validate_source(
        self,
        source_id: str,
        organisation_id: str
    ):
        """
        Validate a source and get accessible folders.
        
        Args:
            source_id: ID of the source to validate
            organisation_id: ID of the organisation
            
        Returns:
            The gRPC response from the organisation service with accessible folders
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.ValidateSourceRequest(
            source_id=source_id,
            organisation_id=organisation_id
        )
        
        try:
            response = self.stub.validateSource(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
    async def list_top_level_folders(self, user_id: str):
        """
        List top-level folders accessible by a user.
        
        Args:
            user_id: ID of the user to list folders for
            
        Returns:
            The gRPC response containing the list of top-level folders
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.ListTopLevelFoldersRequest(
            user_id=user_id
        )
        
        try:
            response = self.stub.listTopLevelFolders(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def update_organisation(
        self,
        organisation_id: str,
        user_id: str,
        name: Optional[str] = None,
        website_url: Optional[str] = None,
        industry: Optional[str] = None,
        logo: Optional[str] = None
    ):
        """
        Update an existing organisation's details.
        
        Args:
            organisation_id: ID of the organisation to update (required)
            name: New name for the organisation (optional)
            website_url: New website URL for the organisation (optional)
            industry: New industry for the organisation (optional)
            logo: New logo URL for the organisation (optional)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.UpdateOrganisationRequest(
            id=organisation_id,
            user_id=user_id,
        )

        if name is not None:
            request.name = name  # If name is "", it's intentional — include it

        if website_url is not None:
            request.website_url = website_url

        if industry is not None:
            request.industry = industry

        if logo is not None:
            request.logo = logo

        try:
            response = self.stub.updateOrganisation(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def add_member_to_department(
        self,
        organisation_id: str,
        department_id: str,
        user_id: str,
        member_id: str,
        role: Optional[str] = None,
        permission: Optional[str] = None
    ):
        """
        Add a member to a department.
        
        Validates:
        - Both users must be part of the same organization
        - The user adding the member must be either:
          - An admin of the department
          - The creator of the organization
        
        Args:
            organisation_id: ID of the organisation
            department_id: ID of the department to add the member to
            user_id: ID of the admin user performing the action
            member_id: ID of the user to add as a member
            role: Role to assign to the member
            permission: Permission level to assign to the member
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.AddMemberToDepartmentRequest(
            organisation_id=organisation_id,
            department_id=department_id,
            user_id=user_id,
            member_id=member_id,
            role=role,
            permission=permission
        )
        
        try:
            response = self.stub.addMemberToDepartment(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def revoke_mcp_key(self, organisation_id: str, user_id: str):
        """
        Revoke an organisation's MCP key.
        
        Args:
            organisation_id: ID of the organisation
            user_id: ID of the user revoking the key (must be admin or creator)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.RevokeMcpKeyRequest(
            organisation_id=organisation_id,
            user_id=user_id
        )
        
        try:
            response = self.stub.revokeMcpKey(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def regenerate_mcp_key(self, organisation_id: str, user_id: str):
        """
        Regenerate an organisation's MCP key.
        
        Args:
            organisation_id: ID of the organisation
            user_id: ID of the user regenerating the key (must be admin or creator)
            
        Returns:
            The gRPC response from the organisation service
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.RegenerateMcpKeyRequest(
            organisation_id=organisation_id,
            user_id=user_id
        )
        
        try:
            response = self.stub.regenerateMcpKey(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)
            
    async def get_organisation_with_admin(self, organisation_id: str):
        """
        Get an organisation with admin details by ID.
        
        Args:
            organisation_id: ID of the organisation
            
        Returns:
            The gRPC response from the organisation service with admin details
            
        Raises:
            GrpcError: If the gRPC call fails
        """
        request = organisation_pb2.GetOrganisationWithAdminRequest(
            id=organisation_id
        )
        
        try:
            response = self.stub.getOrganisationWithAdmin(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)